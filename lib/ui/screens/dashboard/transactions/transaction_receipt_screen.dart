import 'dart:io';
import 'dart:ui' as ui;

import 'package:device_info_plus/device_info_plus.dart';
import 'package:file_saver/file_saver.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter/services.dart';
import 'package:image_gallery_saver/image_gallery_saver.dart';
import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';
import 'package:open_file/open_file.dart';
import 'package:path_provider/path_provider.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:permission_handler/permission_handler.dart';
import 'package:share_plus/share_plus.dart';
import 'package:url_launcher/url_launcher.dart';

class TransactionReceiptScreen extends StatefulWidget {
  const TransactionReceiptScreen({
    super.key,
    required this.receiptArg,
  });

  final ReceiptArg receiptArg;

  @override
  State<TransactionReceiptScreen> createState() =>
      _TransactionReceiptScreenState();
}

class _TransactionReceiptScreenState extends State<TransactionReceiptScreen> {
  final GlobalKey _receiptKey = GlobalKey();
  bool _isModalOpen = false;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _showExportFormatModal();
    });
  }

  _showExportFormatModal() {
    setState(() {
      _isModalOpen = true;
    });

    BsWrapper.bottomSheet(
      context: context,
      widget: ExportFormatModal(
        isDownload: widget.receiptArg.isDownload,
        asImage: widget.receiptArg.isDownload ? _saveToPhotos : _shareAsImage,
        asPdf: widget.receiptArg.isDownload ? _saveAsPDF : _shareAsPDF,
      ),
    ).then((result) {
      setState(() {
        _isModalOpen = false;
      });

      // Handle modal dismissal - if user closes modal without selecting an option
      if (result == null) {
        _showFallbackExportOptions();
      }
      // If user selected an option, the action is already handled in the modal
      // and we don't need to do anything else here
    });
  }

  void _showFallbackExportOptions() {
    setState(() {
      _isModalOpen = true;
    });

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return PopScope(
          onPopInvokedWithResult: (didPop, result) {
            if (didPop) {
              setState(() {
                _isModalOpen = false;
              });
            }
          },
          child: AlertDialog(
            title: Row(
              children: [
                Icon(Icons.download, color: AppColors.primaryBlue),
                SizedBox(width: 8),
                Text('Export Receipt'),
              ],
            ),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text('Would you like to export your receipt before leaving?'),
                SizedBox(height: 16),
                Row(
                  children: [
                    Expanded(
                      child: ElevatedButton.icon(
                        onPressed: () {
                          setState(() {
                            _isModalOpen = false;
                          });
                          Navigator.of(context).pop();
                          if (widget.receiptArg.isDownload) {
                            _saveToPhotos();
                          } else {
                            _shareAsImage();
                          }
                        },
                        icon: Icon(Icons.image, size: 18),
                        label: Text('Image'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppColors.primaryBlue,
                          foregroundColor: AppColors.white,
                        ),
                      ),
                    ),
                    SizedBox(width: 8),
                    Expanded(
                      child: ElevatedButton.icon(
                        onPressed: () {
                          setState(() {
                            _isModalOpen = false;
                          });
                          Navigator.of(context).pop();
                          if (widget.receiptArg.isDownload) {
                            _saveAsPDF();
                          } else {
                            _shareAsPDF();
                          }
                        },
                        icon: Icon(Icons.picture_as_pdf, size: 18),
                        label: Text('PDF'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppColors.primaryBlue,
                          foregroundColor: AppColors.white,
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () {
                  setState(() {
                    _isModalOpen = false;
                  });
                  Navigator.of(context).pop();
                  _navigateBackToTransactionDetails();
                },
                child: Text('Skip'),
              ),
              TextButton(
                onPressed: () {
                  setState(() {
                    _isModalOpen = false;
                  });
                  Navigator.of(context).pop();
                  _showExportFormatModal();
                },
                child: Text('Choose Format'),
              ),
            ],
          ),
        );
      },
    ).then((result) {
      // Handle dialog dismissal by tapping outside or other means
      setState(() {
        _isModalOpen = false;
      });

      // If user dismissed without selecting an option, navigate back
      if (result == null) {
        _navigateBackToTransactionDetails();
      }
    });
  }

  void _navigateBackToTransactionDetails() {
    Navigator.of(context).pop();
  }

  String _getAppStoreLink() {
    // Return appropriate app store link based on platform
    if (Platform.isIOS) {
      return 'https://apps.apple.com/app/korrency/id6495368627'; // Pass App Store ID
    } else {
      return 'https://play.google.com/store/apps/details?id=korrency.mobile.com'; // Pass play store package name
    }
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: !_isModalOpen,
      onPopInvokedWithResult: (didPop, result) {
        if (!didPop && _isModalOpen) {
          // If there's a modal open, close it first
          Navigator.of(context).pop();
        } else if (!didPop) {
          // If no modal is open, navigate back to previous screen
          Navigator.of(context).pop();
        }
      },
      child: Scaffold(
        body: SafeArea(
          bottom: false,
          child: SingleChildScrollView(
            child: Column(
              children: [
                RepaintBoundary(
                  key: _receiptKey,
                  child: _buildReceiptCard(),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildReceiptCard() {
    Transaction transaction = widget.receiptArg.transaction;
    return Material(
      color: AppColors.white,
      child: SizedBox(
        width: Sizer.screenWidth,
        child: Padding(
          padding: EdgeInsets.only(
            top: Sizer.height(20),
            right: Sizer.width(24),
            left: Sizer.width(24),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Text(
                'Transaction Receipt',
                style: FontTypography.text20.withCustomColor(AppColors.blue3B),
              ),
              YBox(8),
              RichText(
                text: TextSpan(
                  children: [
                    TextSpan(
                      text: transaction.amount,
                      style: FontTypography.text34.medium
                          .withCustomColor(AppColors.blue3B),
                    ),
                    TextSpan(
                      text: " ${transaction.currency?.code ?? ""}",
                      style: FontTypography.text20.medium
                          .withCustomColor(AppColors.grayAE),
                    ),
                  ],
                ),
              ),
              YBox(4),
              Text(
                transaction.description ?? '',
                style: FontTypography.text14
                    .withCustomColor(AppColors.primaryBlue),
              ),
              YBox(12),
              Container(
                padding: EdgeInsets.symmetric(
                  horizontal: Sizer.width(8),
                  vertical: Sizer.height(4),
                ),
                decoration: BoxDecoration(
                  color: AppColors.greenF7,
                  borderRadius: BorderRadius.circular(Sizer.radius(10)),
                ),
                child: Text(
                  'Status: ${transaction.status}',
                  style:
                      FontTypography.text12.withCustomColor(AppColors.green99),
                ),
              ),
              SizedBox(height: 32),
              SizedBox(
                width: Sizer.screenWidth,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    if (transaction.beneficiary != null)
                      TransactionListTile(
                        leftText: 'Beneficiary name',
                        rightText: transaction.beneficiary?.accountName ?? '',
                      ),
                    if (transaction.beneficiary != null)
                      TransactionListTile(
                        leftText: 'Beneficiary’s Bank',
                        rightText:
                            transaction.beneficiary?.institutionName ?? '',
                      ),
                    if (transaction.beneficiary != null)
                      TransactionListTile(
                        leftText: 'Beneficiary’s Account #',
                        rightText:
                            transaction.beneficiary?.accountIdentifier ?? '',
                      ),
                    TransactionListTile(
                      leftText: 'Transaction Type',
                      rightText: transaction.category ?? "",
                      reightTextStyle:
                          FontTypography.text16.bold.withCustomColor(
                        AppColors.primaryBlue,
                      ),
                    ),
                    TransactionListTile(
                      leftText: 'Fees',
                      rightText:
                          "${transaction.fees ?? ""} ${transaction.currency?.code ?? ""}",
                    ),
                    if (transaction.rateFormat != null)
                      TransactionListTile(
                        leftText: 'Exchange Rate',
                        rightText: transaction.rateFormat ?? '',
                      ),
                  ],
                ),
              ),
              Column(
                children: [
                  TransactionListTile(
                    leftText: 'Date/Time',
                    rightText: AppUtils.formatDateTime(
                        (transaction.createdAt ?? DateTime.now())
                            .toLocal()
                            .toString()),
                  ),
                  if (transaction.category?.toLowerCase() == "transfer")
                    TransactionListTile(
                      leftText: 'Recipient',
                      rightText: transaction.destination ?? "",
                    ),
                  if (transaction.source != null)
                    TransactionListTile(
                      leftText: 'Source',
                      rightText: transaction.source ?? "",
                    ),
                  if (transaction.category?.toLowerCase() == "transfer")
                    TransactionListTile(
                      leftText: 'Sender',
                      rightText:
                          context.read<AuthUserVM>().user?.fullName ?? '',
                    ),
                  if (transaction.destination != null &&
                      transaction.category?.toLowerCase() != "transfer")
                    TransactionListTile(
                      leftText: 'Destination',
                      rightText: transaction.destination ?? "",
                    ),
                  TransactionListTile(
                    showBorder: transaction.interacSecurityAnswer != null
                        ? true
                        : false,
                    leftText: 'Reference',
                    rightText: transaction.reference ?? "",
                  ),
                  if (transaction.interacSecurityAnswer != null)
                    TransactionListTile(
                      showBorder: false,
                      leftText: 'Interac Security Answer',
                      rightText: transaction.interacSecurityAnswer ?? "",
                    ),
                ],
              ),
              SizedBox(height: 24),
              InkWell(
                onTap: () async {
                  final url = _getAppStoreLink();
                  if (await canLaunchUrl(Uri.parse(url))) {
                    await launchUrl(Uri.parse(url));
                  }
                },
                child: Container(
                  padding: EdgeInsets.symmetric(
                    horizontal: Sizer.width(16),
                    vertical: Sizer.height(10),
                  ),
                  decoration: BoxDecoration(
                    color: AppColors.blue6FF,
                    borderRadius: BorderRadius.circular(Sizer.radius(12)),
                  ),
                  child: Row(
                    children: [
                      imageHelper(
                        AppImages.iconLogo,
                        height: Sizer.height(30),
                        width: Sizer.width(30),
                      ),
                      const XBox(10),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Not on Korrency yet?',
                              style: FontTypography.text12.semiBold
                                  .withCustomColor(AppColors.primaryBlue),
                            ),
                            Text(
                              'Tap here to download the app and get started',
                              style: FontTypography.text12.semiBold
                                  .withCustomColor(AppColors.primaryBlue),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              SizedBox(height: 8),
              Text(
                'Korrency is a registered money service FINTRAC under \nregistration number M24601697',
                textAlign: TextAlign.center,
                style: FontTypography.text12.withCustomColor(AppColors.grayAE),
              ),
              YBox(24),
            ],
          ),
        ),
      ),
    );
  }

  Future<void> _saveToPhotos() async {
    try {
      _showSnackBar('Preparing to save to Photos...');

      // Request photo library permission
      bool hasPermission = await _requestPhotoPermission();
      if (!hasPermission) {
        _showSnackBar('Photo library permission is required to save images');
        return;
      }

      // Capture the receipt as image
      RenderRepaintBoundary boundary = _receiptKey.currentContext!
          .findRenderObject() as RenderRepaintBoundary;
      ui.Image image = await boundary.toImage(pixelRatio: 3.0);
      ByteData? byteData =
          await image.toByteData(format: ui.ImageByteFormat.png);
      Uint8List pngBytes = byteData!.buffer.asUint8List();

      // Save to photo gallery
      final result = await ImageGallerySaver.saveImage(
        pngBytes,
        name:
            'korrency_transaction_receipt_${DateTime.now().millisecondsSinceEpoch}',
        quality: 100,
      );

      if (result['isSuccess'] == true) {
        _showSuccessDialog(
          'Image saved to Photos successfully!',
          result['filePath'] ?? 'Photo Library',
          'photos',
        );
      } else {
        // Handle specific iOS errors
        if (Platform.isIOS && result['error'] != null) {
          if (result['error'].toString().contains('permission')) {
            _showPermissionSettingsDialog();
          } else {
            throw Exception(result['error']);
          }
        } else {
          throw Exception('Failed to save image to gallery');
        }
      }
    } catch (e) {
      printty('Error saving to photos: $e', level: 'error');
      _showSnackBar('Error saving to Photos: ${e.toString()}');
    }
  }

  Future<bool> _requestPhotoPermission() async {
    if (Platform.isIOS) {
      // On iOS, we need to request PHPhotoLibrary (photos) permission
      var status = await Permission.photos.request();
      if (status.isPermanentlyDenied) {
        // The user opted to never again see the permission request dialog for this app
        // You should show them a dialog explaining why you need the permission
        // and direct them to app settings
        _showPermissionSettingsDialog();
        return false;
      }
      return status.isGranted;
    } else if (Platform.isAndroid) {
      // Android permissions
      if (await _isAndroid13OrHigher()) {
        // Android 13+ uses granular media permissions
        var status = await Permission.photos.request();
        return status.isGranted;
      } else {
        // Android 12 and below
        var status = await Permission.storage.request();
        if (!status.isGranted) {
          status = await Permission.manageExternalStorage.request();
        }
        return status.isGranted;
      }
    }
    return false;
  }

  void _showPermissionSettingsDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text('Permission Required'),
          content: Text(
            'Photo library permission is required to save images. '
            'Please enable it in the app settings.',
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text('Cancel'),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                openAppSettings();
              },
              child: Text('Open Settings'),
            ),
          ],
        );
      },
    );
  }

  // Future<bool> _requestPhotoPermission() async {
  //   if (Platform.isIOS) {
  //     // iOS photo library permission
  //     var status = await Permission.photos.request();
  //     return status.isGranted;
  //   } else if (Platform.isAndroid) {
  //     // Android permissions vary by version
  //     if (await _isAndroid13OrHigher()) {
  //       // Android 13+ uses granular media permissions
  //       var status = await Permission.photos.request();
  //       return status.isGranted;
  //     } else {
  //       // Android 12 and below
  //       var status = await Permission.storage.request();
  //       if (!status.isGranted) {
  //         status = await Permission.manageExternalStorage.request();
  //       }
  //       return status.isGranted;
  //     }
  //   }
  //   return false;
  // }

  Future<bool> _isAndroid13OrHigher() async {
    if (Platform.isAndroid) {
      var androidInfo = await DeviceInfoPlugin().androidInfo;
      return androidInfo.version.sdkInt >= 33;
    }
    return false;
  }

  Future<void> _saveAsPDF() async {
    try {
      _showSnackBar('Generating PDF...');

      final pdf = await _generatePDF();
      Uint8List pdfBytes = await pdf.save();

      // Generate filename with timestamp
      String fileName =
          'korrency_transaction_receipt_${DateTime.now().millisecondsSinceEpoch}';

      try {
        // Try using file_saver first
        String? filePath = await FileSaver.instance.saveFile(
          name: fileName,
          bytes: pdfBytes,
          ext: 'pdf',
          mimeType: MimeType.pdf,
        );

        _showSuccessDialog('PDF saved successfully!', filePath, 'pdf');
      } catch (e) {
        // Fallback to manual saving
        await _savePDFManually(pdfBytes, fileName);
      }
    } catch (e) {
      _showSnackBar('Error saving PDF: $e');
    }
  }

  Future<void> _savePDFManually(Uint8List bytes, String fileName) async {
    try {
      // Request storage permission for manual saving
      if (Platform.isAndroid) {
        var status = await Permission.storage.request();
        if (!status.isGranted) {
          status = await Permission.manageExternalStorage.request();
        }
        if (!status.isGranted) {
          _showSnackBar('Storage permission required to save PDF');
          return;
        }
      }

      Directory? directory;
      String? fullPath;

      if (Platform.isAndroid) {
        // Try Documents or Download directory for PDFs
        List<String> possiblePaths = [
          '/storage/emulated/0/Documents',
          '/storage/emulated/0/Download',
        ];

        for (String path in possiblePaths) {
          directory = Directory(path);
          if (await directory.exists()) {
            fullPath = '$path/$fileName.pdf';
            break;
          }
        }

        // Fallback to external storage directory
        if (fullPath == null) {
          directory = await getExternalStorageDirectory();
          if (directory != null) {
            fullPath = '${directory.path}/$fileName.pdf';
          }
        }
      } else {
        directory = await getApplicationDocumentsDirectory();
        fullPath = '${directory.path}/$fileName.pdf';
      }

      if (fullPath != null) {
        File file = File(fullPath);
        await file.writeAsBytes(bytes);

        // Trigger media scan on Android
        if (Platform.isAndroid) {
          await _triggerMediaScan(fullPath);
        }

        _showSuccessDialog('PDF saved to Documents!', fullPath, 'pdf');
      } else {
        throw Exception('Could not find storage directory');
      }
    } catch (e) {
      _showSnackBar('Error in manual PDF save: $e');
    }
  }

  Future<void> _triggerMediaScan(String filePath) async {
    try {
      // Use platform channel to trigger media scan
      const platform = MethodChannel('media_scanner');
      await platform.invokeMethod('scanFile', {'path': filePath});
    } catch (e) {
      // If platform channel fails, try alternative approach
      try {
        // Create an intent to scan the file
        await Process.run('am', [
          'broadcast',
          '-a',
          'android.intent.action.MEDIA_SCANNER_SCAN_FILE',
          '-d',
          'file://$filePath'
        ]);
      } catch (processError) {
        // Silent fail - the file is still saved, just might not appear in gallery immediately
        printty('Media scan failed, but file is saved: $processError',
            level: 'warning');
      }
    }
  }

  Future<void> _shareAsImage() async {
    try {
      _showSnackBar('Preparing image for sharing...');

      // Capture the receipt as image
      RenderRepaintBoundary boundary = _receiptKey.currentContext!
          .findRenderObject() as RenderRepaintBoundary;
      ui.Image image = await boundary.toImage(pixelRatio: 3.0);
      ByteData? byteData =
          await image.toByteData(format: ui.ImageByteFormat.png);
      Uint8List pngBytes = byteData!.buffer.asUint8List();

      // Save to temporary directory for sharing
      Directory tempDir = await getTemporaryDirectory();
      File tempFile = File('${tempDir.path}/transaction_receipt.png');
      await tempFile.writeAsBytes(pngBytes);

      // Share the file
      await Share.shareXFiles(
        [XFile(tempFile.path)],
        text: 'Korrency Transaction Receipt',
        subject:
            'Korrency Transaction Receipt - ${widget.receiptArg.transaction.amount} ${widget.receiptArg.transaction.currency?.code ?? ""}',
      );

      // Navigate back to transaction details after successful sharing
      _navigateBackToTransactionDetails();
    } catch (e) {
      _showSnackBar('Error sharing image: $e');
    }
  }

  Future<void> _shareAsPDF() async {
    try {
      _showSnackBar('Preparing PDF for sharing...');

      final pdf = await _generatePDF();

      // Save to temporary directory for sharing
      Directory tempDir = await getTemporaryDirectory();
      File tempFile = File('${tempDir.path}/transaction_receipt.pdf');
      await tempFile.writeAsBytes(await pdf.save());

      // Share the file
      await Share.shareXFiles(
        [XFile(tempFile.path)],
        text: 'Transaction Receipt PDF',
        subject:
            'Transaction Receipt - ${widget.receiptArg.transaction.amount} ${widget.receiptArg.transaction.currency?.code ?? ""}',
      );

      // Navigate back to transaction details after successful sharing
      _navigateBackToTransactionDetails();
    } catch (e) {
      _showSnackBar('Error sharing PDF: $e');
    }
  }

  Future<pw.Document> _generatePDF() async {
    Transaction transaction = widget.receiptArg.transaction;
    final pdf = pw.Document();

    pdf.addPage(
      pw.Page(
        pageFormat: PdfPageFormat.a4,
        build: (pw.Context context) {
          return pw.Container(
            padding: pw.EdgeInsets.all(32),
            child: pw.Column(
              crossAxisAlignment: pw.CrossAxisAlignment.center,
              children: [
                pw.Text(
                  'Transaction Receipt',
                  style: pw.TextStyle(
                    fontSize: 24,
                    fontWeight: pw.FontWeight.bold,
                  ),
                ),
                pw.SizedBox(height: 24),
                pw.RichText(
                  text: pw.TextSpan(
                    children: [
                      pw.TextSpan(
                        text: transaction.amount,
                        style: pw.TextStyle(
                          fontSize: 32,
                          fontWeight: pw.FontWeight.bold,
                        ),
                      ),
                      pw.TextSpan(
                        text: " ${transaction.currency?.code ?? ""}",
                        style: pw.TextStyle(
                          fontSize: 20,
                          fontWeight: pw.FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                ),

                // pw.Text(
                //   '${transaction.amount} ${transaction.currency?.code}',
                //   style: pw.TextStyle(
                //     fontSize: 32,
                //     fontWeight: pw.FontWeight.bold,
                //   ),
                // ),
                pw.SizedBox(height: 8),
                pw.Text(
                  transaction.description ?? '',
                  style: pw.TextStyle(fontSize: 16),
                ),
                pw.SizedBox(height: 16),
                pw.Container(
                  padding: pw.EdgeInsets.all(8),
                  decoration: pw.BoxDecoration(
                    border: pw.Border.all(color: PdfColors.green),
                    borderRadius: pw.BorderRadius.circular(4),
                  ),
                  child: pw.Text(
                    'Status: ${transaction.status}',
                    style: pw.TextStyle(color: PdfColors.green),
                  ),
                ),
                pw.SizedBox(height: 32),
                ..._buildPDFRows(),
                pw.SizedBox(height: 24),
                pw.Container(
                  padding: pw.EdgeInsets.all(16),
                  decoration: pw.BoxDecoration(
                    color: PdfColors.blue50,
                    borderRadius: pw.BorderRadius.circular(4),
                  ),
                  child: pw.Column(
                    children: [
                      pw.Text(
                        'Not on Korrency yet?',
                        style: pw.TextStyle(fontWeight: pw.FontWeight.bold),
                      ),
                      pw.UrlLink(
                        destination: _getAppStoreLink(),
                        child: pw.Text(
                          'Tap here to download the app and get started',
                          style: pw.TextStyle(
                            color: PdfColors.blue,
                            decoration: pw.TextDecoration.underline,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                pw.SizedBox(height: 16),
                pw.Text(
                  'Korrency is a registered money service FINTRAC under registration number M24601697',
                  style: pw.TextStyle(fontSize: 10),
                  textAlign: pw.TextAlign.center,
                ),
              ],
            ),
          );
        },
      ),
    );

    return pdf;
  }

  List<pw.Widget> _buildPDFRows() {
    final rows = [
      [
        'Beneficiary name',
        widget.receiptArg.transaction.beneficiary?.accountName ?? ''
      ],
      [
        'Beneficiary’s Bank',
        widget.receiptArg.transaction.beneficiary?.institutionName ?? ''
      ],
      [
        'Beneficiary’s Account #',
        widget.receiptArg.transaction.beneficiary?.accountIdentifier ?? ''
      ],
      ['Transaction Type', widget.receiptArg.transaction.category ?? ""],
      [
        'Fees',
        '${widget.receiptArg.transaction.fees ?? ""} ${widget.receiptArg.transaction.currency?.code ?? ""}'
      ],
      if (widget.receiptArg.transaction.rateFormat != null)
        ['Exchange Rate', widget.receiptArg.transaction.rateFormat ?? ''],
      [
        'Date/Time',
        AppUtils.formatDateTime(
            (widget.receiptArg.transaction.createdAt ?? DateTime.now())
                .toLocal()
                .toString())
      ],
      ['Recipient', widget.receiptArg.transaction.destination ?? ""],
      ['Source', widget.receiptArg.transaction.source ?? ""],
      ['Sender', context.read<AuthUserVM>().user?.fullName ?? ''],
      ['Session ID', widget.receiptArg.transaction.sessionId ?? ""],
      ['Reference', widget.receiptArg.transaction.reference ?? ""],
    ];

    return rows
        .map((row) => pw.Container(
              padding: pw.EdgeInsets.symmetric(vertical: 8),
              decoration: pw.BoxDecoration(
                border: pw.Border(
                  bottom: pw.BorderSide(color: PdfColors.grey300, width: 0.5),
                ),
              ),
              child: pw.Row(
                mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                children: [
                  pw.Expanded(
                    flex: 2,
                    child: pw.Text(row[0], style: pw.TextStyle(fontSize: 12)),
                  ),
                  pw.Expanded(
                    flex: 3,
                    child: pw.Text(
                      row[1],
                      style: pw.TextStyle(
                        fontSize: 12,
                        fontWeight: pw.FontWeight.bold,
                      ),
                      textAlign: pw.TextAlign.right,
                    ),
                  ),
                ],
              ),
            ))
        .toList();
  }

  void _showSuccessDialog(String message, String filePath, String fileType) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Row(
            children: [
              Icon(Icons.check_circle, color: Colors.green, size: 28),
              SizedBox(width: 8),
              Text('Success!'),
            ],
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(message),
              SizedBox(height: 12),
              if (fileType == 'photos')
                Container(
                  padding: EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.green[50],
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Row(
                    children: [
                      Icon(Icons.photo_library, color: Colors.green, size: 16),
                      SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          'Image saved to your Photos app',
                          style:
                              TextStyle(fontSize: 12, color: Colors.green[700]),
                        ),
                      ),
                    ],
                  ),
                ),
              if (fileType != 'photos') ...[
                SizedBox(height: 8),
                Text(
                  'File location:',
                  style: TextStyle(fontWeight: FontWeight.bold, fontSize: 12),
                ),
                SizedBox(height: 4),
                Container(
                  padding: EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.grey[100],
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Text(
                    filePath,
                    style: TextStyle(fontSize: 11, fontFamily: 'monospace'),
                  ),
                ),
              ],
            ],
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                _navigateBackToTransactionDetails();
              },
              child: Text('Close'),
            ),
            if (fileType != 'photos')
              ElevatedButton.icon(
                onPressed: () async {
                  Navigator.of(context).pop();
                  await _openFile(filePath);
                  _navigateBackToTransactionDetails();
                },
                icon: Icon(Icons.open_in_new, size: 18),
                label: Text('Open File'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.blue,
                  foregroundColor: Colors.white,
                ),
              ),
          ],
        );
      },
    );
  }

  Future<void> _openFile(String filePath) async {
    try {
      final result = await OpenFile.open(filePath);
      if (result.type != ResultType.done) {
        // If opening fails, show file location info
        _showFileLocationDialog(filePath);
      }
    } catch (e) {
      _showFileLocationDialog(filePath);
    }
  }

  void _showFileLocationDialog(String filePath) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text('File Location'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('File saved to:'),
              SizedBox(height: 8),
              Container(
                padding: EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.grey[100],
                  borderRadius: BorderRadius.circular(4),
                ),
                child: SelectableText(
                  filePath,
                  style: TextStyle(fontSize: 12, fontFamily: 'monospace'),
                ),
              ),
              SizedBox(height: 12),
              Text(
                'You can find this file in your device\'s file manager.',
                style: TextStyle(fontSize: 12, color: Colors.grey[600]),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text('OK'),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                Clipboard.setData(ClipboardData(text: filePath));
                _showSnackBar('File path copied to clipboard');
              },
              child: Text('Copy Path'),
            ),
          ],
        );
      },
    );
  }

  void _showSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        duration: Duration(seconds: 3),
      ),
    );
  }
}
